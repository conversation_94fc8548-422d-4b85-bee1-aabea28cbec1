// Copyright 2025 Amazon.com, Inc. or its affiliates. All Rights Reserved.
// SPDX-License-Identifier: Apache-2.0

package software.aws.toolkits.jetbrains.services.codewhisperer.popup

import com.intellij.codeInsight.inline.completion.InlineCompletionEvent
import com.intellij.codeInsight.inline.completion.InlineCompletionProvider
import com.intellij.codeInsight.inline.completion.InlineCompletionProviderID
import com.intellij.codeInsight.inline.completion.InlineCompletionProviderPresentation
import com.intellij.codeInsight.inline.completion.InlineCompletionRequest
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionElement
import com.intellij.codeInsight.inline.completion.elements.InlineCompletionGrayTextElement
import com.intellij.codeInsight.inline.completion.session.InlineCompletionSession
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSingleSuggestion
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionSuggestion
import com.intellij.codeInsight.inline.completion.suggestion.InlineCompletionVariant
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.ReadAction
import com.intellij.openapi.application.runInEdt
import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.diagnostic.thisLogger
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.project.Project
import com.intellij.vcs.log.runInEdtAsync
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.future.await
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.eclipse.lsp4j.jsonrpc.messages.Either
import software.aws.toolkits.jetbrains.core.coroutines.getCoroutineBgContext
import software.aws.toolkits.jetbrains.services.amazonq.lsp.AmazonQLspService
import software.aws.toolkits.jetbrains.services.amazonq.lsp.model.aws.textDocument.InlineCompletionItem
import software.aws.toolkits.jetbrains.services.amazonq.lsp.model.aws.textDocument.InlineCompletionListWithReferences
import software.aws.toolkits.jetbrains.services.codewhisperer.model.TriggerTypeInfo
import software.aws.toolkits.jetbrains.services.codewhisperer.service.CodeWhispererService
import software.aws.toolkits.jetbrains.services.codewhisperer.util.CodeWhispererConstants
//import software.aws.toolkits.jetbrains.services.codewhisperer.util.isCodeWhispererEnabled
//import software.aws.toolkits.jetbrains.services.codewhisperer.util.isQExpired
//import software.aws.toolkits.jetbrains.services.codewhisperer.util.isValidCodeWhispererFile
import software.aws.toolkits.telemetry.CodewhispererTriggerType
import javax.swing.JComponent
import javax.swing.JLabel

class QInlineCompletionProvider(private val cs: CoroutineScope) : InlineCompletionProvider {
    override val id: InlineCompletionProviderID = InlineCompletionProviderID("Amazon Q")
    override val providerPresentation: InlineCompletionProviderPresentation
        get() = object : InlineCompletionProviderPresentation {
            override fun getTooltip(project: Project?): JComponent {
                return JLabel("hahahah")
//                TODO("Not yet implemented")
            }

        }
    companion object {
//        fun getInstance(): QInlineCompletionProvider {
//            return QInlineCompletionProvider()
//        }
    }
    private val logger = thisLogger()

    // Store active channels for pagination
    private val activeChannels = mutableMapOf<String, Channel<InlineCompletionElement>>()

//    fun addPaginatedResults(editor: Editor, event: InlineCompletionEvent, newItems: List<InlineCompletionItem>) {
//        // Get access to the variants provider (this would need to be exposed)
//        InlineCompletionSession.getOrNull(editor)?.provider?.suggestionUpdateManager?.update(event) { snapshot ->
//            // Create new elements from your paginated results
//            val newElements = newItems.map { item ->
//                InlineCompletionGrayTextElement(item.insertText)
//            }
//
//            // Return updated snapshot with additional elements
//            UpdateResult.Changed(
//                snapshot.copy(elements = snapshot.elements + newElements)
//            )
//        }
//    }

    override suspend fun getSuggestion(request: InlineCompletionRequest): InlineCompletionSuggestion {
        val editor = request.editor
        val project = editor.project ?: return InlineCompletionSuggestion.Empty

        logger.debug("Getting inline completion suggestion for offset: ${request.endOffset}")

        try {
            val triggerTypeInfo = TriggerTypeInfo(
                triggerType = CodewhispererTriggerType.AutoTrigger,
                automatedTriggerType = software.aws.toolkits.jetbrains.services.codewhisperer.service.CodeWhispererAutomatedTriggerType.Classifier()
            )

            // Get first page of completions
            val completionResult = withContext(getCoroutineBgContext()) {
                AmazonQLspService.executeIfRunning(project) { server ->
                    val params = createInlineCompletionParams(editor, triggerTypeInfo, null)
                    server.inlineCompletionWithReferences(params)
                }?.await()
            }

//            val completion = completionResult ?: return InlineCompletionSuggestion.Empty
            val completion = InlineCompletionListWithReferences(
                items = listOf(
                    InlineCompletionItem("item1", "(x, y) {\n        return x + y\n    }"),
                ),
                sessionId = "sessionIdhha",
                partialResultToken = Either.forLeft("nextTokenhaha")
            )

            if (completion.items.isEmpty()) {
                logger.debug("No completions received from LSP server")
                return InlineCompletionSuggestion.Empty
            }

            logger.debug("Received ${completion.items.size} completions, nextToken: ${completion.partialResultToken}")

            // Store completion state for telemetry and import handling
            QInlineCompletionStateManager.getInstance(project).storeCompletionState(
                completion.sessionId,
                completion,
                request.endOffset
            )

            val nextToken = completion.partialResultToken

            // Start pagination in background if there's a nextToken
            if (nextToken != null && !nextToken.left.isNullOrEmpty()) {
                cs.launch {
                    startPaginationInBackground(
                        project,
                        editor,
                        triggerTypeInfo,
                        completion.sessionId,
                        nextToken
                    )
                }
            }

            return object : InlineCompletionSuggestion {
                override suspend fun getVariants(): List<InlineCompletionVariant> {
                    return completion.items.map { item ->
                        val channel = Channel<InlineCompletionElement>(Channel.UNLIMITED)
                        val flow = channel.receiveAsFlow()

                        // Store the channel for this item so we can add to it later
                        val channelKey = completion.sessionId
                        activeChannels[channelKey] = channel

                        // Emit the initial element
                        channel.trySend(InlineCompletionGrayTextElement(item.insertText))

                        InlineCompletionVariant.build(elements = flow)
                    }
                }
            }

        } catch (e: Exception) {
            logger.warn("Error getting inline completion suggestion", e)
            return InlineCompletionSuggestion.Empty
        }
    }

    private suspend fun startPaginationInBackground(
        project: Project,
        editor: Editor,
        triggerTypeInfo: TriggerTypeInfo,
        sessionId: String,
        initialNextToken: Either<String, Int>
    ) {
        // Launch coroutine for background pagination
        withContext(getCoroutineBgContext()) {
//        GlobalScope.launch(getCoroutineBgContext()) {
            var nextToken: Either<String, Int>? = initialNextToken

            while (nextToken != null && !nextToken.left.isNullOrEmpty()) {
                try {
                    logger.debug("Fetching next page with token: $nextToken")

                    var nextPageResult = AmazonQLspService.executeIfRunning(project) { server ->
                        val params = createInlineCompletionParams(editor, triggerTypeInfo, nextToken)
                        server.inlineCompletionWithReferences(params)
                    }?.await()

                    nextPageResult = InlineCompletionListWithReferences(
                        items = listOf(
                            InlineCompletionItem("item2", "suggestion 2"),
                        ),
                        sessionId = "sessionIdhha",
                        partialResultToken = Either.forLeft("")
                    )

                    logger.debug("Received ${nextPageResult.items.size} items from pagination")

                    // Add new items to existing channels
                    nextPageResult.items.forEach { newItem ->
                        val channelKey = sessionId
                        val existingChannel = activeChannels[channelKey]

                        if (existingChannel != null) {
                            // Add to existing channel
                            val success = existingChannel.trySend(InlineCompletionGrayTextElement(newItem.insertText))
                            if (success.isSuccess) {
                                logger.debug("Added paginated item to existing channel: ${newItem.itemId}")
                            } else {
                                logger.warn("Failed to add paginated item to channel: ${newItem.itemId}")
                            }
                        } else {
                            // This is a new item - we can't add new variants dynamically
                            // but we can log it for debugging
                            logger.debug("New item from pagination (cannot add new variant): ${newItem.itemId}")
                        }
                    }

                    // Update state manager with new items
                    val stateManager = QInlineCompletionStateManager.getInstance(project)
                    stateManager.appendCompletionItems(sessionId, nextPageResult.items)

                    nextToken = nextPageResult.partialResultToken

                } catch (e: Exception) {
                    logger.warn("Error during pagination", e)
                    break
                }
            }

            logger.debug("Pagination completed for session: $sessionId")

            // Close channels when pagination is complete
            activeChannels.keys.filter { it.startsWith("$sessionId") }.forEach { key ->
                activeChannels[key]?.close()
                activeChannels.remove(key)
            }
        }
    }

    private fun createInlineCompletionParams(
        editor: Editor,
        triggerTypeInfo: TriggerTypeInfo,
        nextToken: Either<String, Int>?
    ) = CodeWhispererService.getInstance().createInlineCompletionParams(
        editor,
        triggerTypeInfo,
        nextToken
    )

    /**
     * Clean up channels for a specific session
     */
    fun cleanupSession(sessionId: String) {
        activeChannels.keys.filter { it.startsWith("${sessionId}_") }.forEach { key ->
            activeChannels[key]?.close()
            activeChannels.remove(key)
        }
        logger.debug("Cleaned up channels for session: $sessionId")
    }

    /**
     * Clean up all active channels
     */
    fun cleanupAllChannels() {
        activeChannels.values.forEach { it.close() }
        activeChannels.clear()
        logger.debug("Cleaned up all active channels")
    }

    override fun isEnabled(event: InlineCompletionEvent): Boolean {
        return true
    }

//    override fun isEnabled(event: InlineCompletionEvent): Boolean {
//        val editor = event.editor
//        val project = editor.project ?: return false
//
//         Check if CodeWhisperer is enabled for this project
//        if (!isCodeWhispererEnabled(project)) {
//            return false
//        }
//
//         Check authentication state
//        if (isQExpired(project)) {
//            return false
//        }
//
//         Check if this is a valid file type for CodeWhisperer
//        val file = ReadAction.compute<Boolean, RuntimeException> {
//            val psiFile = event.file
//            psiFile?.let { isValidCodeWhispererFile(it) } ?: false
//        }
//
//        if (!file) {
//            return false
//        }
//
//         Check if we can do invocation (not already in progress, etc.)
//        val canInvoke = CodeWhispererService.getInstance().canDoInvocation(
//            editor,
//            CodewhispererTriggerType.AutoTrigger
//        )
//
//        return canInvoke
//    }
}
